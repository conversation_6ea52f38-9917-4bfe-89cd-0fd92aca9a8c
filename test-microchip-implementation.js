/**
 * Simple test script to verify the Microchip API implementation
 * This script tests the updated implementation that exactly matches MPLAB AI CHATBOTAPI.ts
 */

const { buildApiHandler } = require('./Roo-Code/src/api/index.js');

async function testMicrochipImplementation() {
    console.log('🧪 Testing Microchip API Implementation (MPLAB AI Compatible)...\n');

    try {
        // Configuration matching the MPLAB AI CHATBOTAPI approach
        const providerSettings = {
            apiProvider: 'microchip',
            microchipApiKey: 'Your Key',
            // Model ID is ignored - always uses "microchip-chatbot-internal"
            apiModelId: 'any-model-id-will-be-ignored',
            // Base URL defaults to CHATBOTAPI endpoint
        };

        console.log('📋 Configuration:');
        console.log(`  Provider: ${providerSettings.apiProvider}`);
        console.log(`  Model: Always "microchip-chatbot-internal" (hardcoded like MPLAB AI)`);
        console.log(`  Base URL: https://ai-apps.microchip.com/CodeGPTAPI/api/Chat/CodeCompletionLLM`);
        console.log(`  API Key: ${providerSettings.microchipApiKey ? '***' + providerSettings.microchipApiKey.slice(-4) : 'Not provided'}\n`);

        // Create API handler
        const apiHandler = buildApiHandler(providerSettings);
        console.log('✅ API handler created successfully');

        // Get model information
        const { id: modelId, info: modelInfo } = apiHandler.getModel();
        console.log(`📊 Model Info:`);
        console.log(`  ID: ${modelId}`);
        console.log(`  Context Window: ${modelInfo.contextWindow}`);
        console.log(`  Max Tokens: ${modelInfo.maxTokens}`);
        console.log(`  Supports Images: ${modelInfo.supportsImages}`);
        console.log(`  Supports Prompt Cache: ${modelInfo.supportsPromptCache}\n`);

        // Test simple completion
        console.log('🔄 Testing completePrompt method...');
        const prompt = 'What is a microcontroller?';
        const completion = await apiHandler.completePrompt(prompt);
        console.log(`✅ Completion received (${completion.length} characters)`);
        console.log(`📝 Response preview: ${completion.substring(0, 100)}...\n`);

        // Test streaming message
        console.log('🔄 Testing createMessage streaming...');
        const systemPrompt = 'You are a helpful assistant for embedded systems development.';
        const messages = [{ role: 'user', content: 'Explain the difference between UART and SPI communication protocols.' }];

        const messageStream = apiHandler.createMessage(systemPrompt, messages);

        let fullResponse = '';
        let chunkCount = 0;
        let tokenStats = { input: 0, output: 0 };

        for await (const chunk of messageStream) {
            if (chunk.type === 'text' && chunk.text) {
                fullResponse += chunk.text;
                chunkCount++;
                process.stdout.write('.');
            } else if (chunk.type === 'usage') {
                tokenStats.input = chunk.inputTokens || 0;
                tokenStats.output = chunk.outputTokens || 0;
            }
        }

        console.log(`\n✅ Streaming completed`);
        console.log(`📊 Stats:`);
        console.log(`  Chunks received: ${chunkCount}`);
        console.log(`  Total response length: ${fullResponse.length} characters`);
        console.log(`  Input tokens: ${tokenStats.input}`);
        console.log(`  Output tokens: ${tokenStats.output}`);
        console.log(`📝 Response preview: ${fullResponse.substring(0, 200)}...\n`);

        console.log('🎉 All tests passed! Microchip API implementation is working correctly.');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
        console.error('📋 Error details:', error);
        process.exit(1);
    }
}

// Run the test
if (require.main === module) {
    testMicrochipImplementation().catch(console.error);
}

module.exports = { testMicrochipImplementation };
